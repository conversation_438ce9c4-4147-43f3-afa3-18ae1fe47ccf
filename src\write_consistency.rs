use std::collections::HashMap;
use std::sync::Arc;
use std::time::Duration;
use tokio::time::sleep;
use futures::future::join_all;
use log::{debug, error, info, warn};


/// Write consistency levels for synchronous replication
#[derive(Debug, <PERSON>lone, PartialEq)]
pub enum WriteConsistency {
    All,    // All peer nodes must respond successfully
    Quorum, // Majority (or quorum_value) of peer nodes must respond successfully
    One,    // At least one peer node must respond successfully
}

impl WriteConsistency {
    pub fn from_string(s: &str) -> Result<Self, String> {
        match s.to_uppercase().as_str() {
            "ALL" => Ok(WriteConsistency::All),
            "QUORUM" => Ok(WriteConsistency::Quorum),
            "ONE" => Ok(WriteConsistency::One),
            _ => Err(format!("Invalid write consistency: {}. Must be ALL, QUORUM, or ONE", s)),
        }
    }
}

/// Write operation types for peer Redis nodes
#[derive(Debug, <PERSON><PERSON>)]
pub enum WriteOperation {
    Set { key: String, value: String },
    Delete { key: String },
    SetEx { key: String, value: String, ttl: u64 },
    SetExpiry { key: String, ttl: i64 },
    IncrBy { key: String, value: i64 },
    DecrBy { key: String, value: i64 },
    IncrByFloat { key: String, value: f64 },
    HSet { key: String, field: String, value: String },
    HIncrBy { key: String, field: String, value: i64 },
    HDecrBy { key: String, field: String, value: i64 },
    HIncrByFloat { key: String, field: String, value: f64 },
}

/// Redis connection pool for peer nodes
pub struct PeerRedisPool {
    pools: HashMap<String, deadpool_redis::Pool>,
    retry_count: u32,
    retry_delay: Duration,
    pool_size: usize,
}

impl PeerRedisPool {
    pub async fn new(peer_nodes: &[String], pool_size: usize, retry_count: u32, retry_delay_ms: u64) -> Self {
        info!("Creating peer Redis pools with size {} for {} nodes", pool_size, peer_nodes.len());

        let mut pools = HashMap::new();

        for node_url in peer_nodes {
            match deadpool_redis::Manager::new(node_url.clone()) {
                Ok(manager) => {
                    match deadpool_redis::Pool::builder(manager)
                        .max_size(pool_size)
                        .runtime(deadpool_redis::Runtime::Tokio1)
                        .wait_timeout(Some(Duration::from_millis(200))) // Increased from 100ms
                        .create_timeout(Some(Duration::from_secs(3)))   // Increased from 2s
                        .recycle_timeout(Some(Duration::from_secs(120))) // Increased from 60s
                        .build()
                    {
                        Ok(pool) => {
                            pools.insert(node_url.clone(), pool);
                            info!("Created Redis pool for peer node: {} with size {}", node_url, pool_size);
                        }
                        Err(e) => {
                            error!("Failed to create Redis pool for peer node {}: {}", node_url, e);
                        }
                    }
                }
                Err(e) => {
                    error!("Failed to create Redis manager for peer node {}: {}", node_url, e);
                }
            }
        }

        Self {
            pools,
            retry_count,
            retry_delay: Duration::from_millis(retry_delay_ms),
            pool_size,
        }
    }

    pub async fn execute_write_operation(&self, node_url: &str, operation: &WriteOperation) -> bool {
        if let Some(pool) = self.pools.get(node_url) {
            for attempt in 0..=self.retry_count {
                match pool.get().await {
                    Ok(mut conn) => {
                        let result = match operation {
                            WriteOperation::Set { key, value } => {
                                use deadpool_redis::redis::AsyncCommands;
                                conn.set::<_, _, ()>(key, value).await
                            }
                            WriteOperation::Delete { key } => {
                                use deadpool_redis::redis::AsyncCommands;
                                conn.del::<_, ()>(key).await
                            }
                            WriteOperation::SetEx { key, value, ttl } => {
                                use deadpool_redis::redis::AsyncCommands;
                                conn.set_ex::<_, _, ()>(key, value, *ttl).await
                            }
                            WriteOperation::SetExpiry { key, ttl } => {
                                use deadpool_redis::redis::AsyncCommands;
                                conn.expire::<_, ()>(key, *ttl).await
                            }
                            WriteOperation::IncrBy { key, value } => {
                                use deadpool_redis::redis::AsyncCommands;
                                conn.incr::<_, _, ()>(key, *value).await
                            }
                            WriteOperation::DecrBy { key, value } => {
                                use deadpool_redis::redis::AsyncCommands;
                                conn.decr::<_, _, ()>(key, *value).await
                            }
                            WriteOperation::IncrByFloat { key, value } => {
                                use deadpool_redis::redis::cmd;
                                cmd("INCRBYFLOAT")
                                    .arg(key)
                                    .arg(*value)
                                    .query_async(&mut *conn)
                                    .await
                            }
                            WriteOperation::HSet { key, field, value } => {
                                use deadpool_redis::redis::AsyncCommands;
                                conn.hset::<_, _, _, ()>(key, field, value).await
                            }
                            WriteOperation::HIncrBy { key, field, value } => {
                                use deadpool_redis::redis::AsyncCommands;
                                conn.hincr::<_, _, _, ()>(key, field, *value).await
                            }
                            WriteOperation::HDecrBy { key, field, value } => {
                                use deadpool_redis::redis::AsyncCommands;
                                conn.hincr::<_, _, _, ()>(key, field, -*value).await
                            }
                            WriteOperation::HIncrByFloat { key, field, value } => {
                                use deadpool_redis::redis::cmd;
                                cmd("HINCRBYFLOAT")
                                    .arg(key)
                                    .arg(field)
                                    .arg(*value)
                                    .query_async(&mut *conn)
                                    .await
                            }
                        };

                        match result {
                            Ok(_) => {
                                if attempt > 0 {
                                    debug!("Write operation succeeded on peer {} after {} retries", node_url, attempt);
                                }
                                return true;
                            }
                            Err(e) => {
                                if attempt < self.retry_count {
                                    warn!("Write operation failed on peer {} (attempt {}/{}): {}. Retrying...",
                                          node_url, attempt + 1, self.retry_count + 1, e);
                                    sleep(self.retry_delay).await;
                                } else {
                                    error!("Write operation failed on peer {} after {} attempts: {}",
                                           node_url, self.retry_count + 1, e);
                                }
                            }
                        }
                    }
                    Err(e) => {
                        if attempt < self.retry_count {
                            warn!("Failed to get connection to peer {} (attempt {}/{}): {}. Retrying...",
                                  node_url, attempt + 1, self.retry_count + 1, e);
                            sleep(self.retry_delay).await;
                        } else {
                            error!("Failed to get connection to peer {} after {} attempts: {}",
                                   node_url, self.retry_count + 1, e);
                        }
                    }
                }
            }
        } else {
            error!("No Redis pool found for peer node: {}", node_url);
        }
        false
    }

    /// Get pool statistics for monitoring
    pub fn get_pool_stats(&self) -> HashMap<String, (usize, usize)> {
        let mut stats = HashMap::new();
        for (node_url, pool) in &self.pools {
            let status = pool.status();
            stats.insert(
                node_url.clone(),
                (status.size, self.pool_size) // (current_size, max_size)
            );
        }
        stats
    }

    /// Get the configured pool size
    pub fn get_pool_size(&self) -> usize {
        self.pool_size
    }
}

/// Write consistency checker for synchronous replication
pub struct WriteConsistencyChecker {
    peer_redis_pool: Arc<PeerRedisPool>,
    write_consistency: WriteConsistency,
    quorum_value: usize,
}

impl WriteConsistencyChecker {
    pub async fn new(
        peer_nodes: &[String],
        write_consistency: WriteConsistency,
        quorum_value: usize,
        pool_size: usize,
        retry_count: u32,
        retry_delay_ms: u64,
    ) -> Self {
        let peer_redis_pool = Arc::new(
            PeerRedisPool::new(peer_nodes, pool_size, retry_count, retry_delay_ms).await
        );

        Self {
            peer_redis_pool,
            write_consistency,
            quorum_value,
        }
    }

    /// Execute write operation on peer Redis nodes and check consistency
    pub async fn execute_write_with_consistency(&self, peer_nodes: &[String], operation: WriteOperation) -> bool {
        if peer_nodes.is_empty() {
            return true; // No peers to write to, consider it successful
        }

        // Calculate required successes upfront
        let required_successes = match self.write_consistency {
            WriteConsistency::All => peer_nodes.len(),
            WriteConsistency::Quorum => self.quorum_value.min(peer_nodes.len()),
            WriteConsistency::One => 1,
        };

        // For ALL consistency, we must try all nodes
        if matches!(self.write_consistency, WriteConsistency::All) {
            return self.execute_write_all_nodes(peer_nodes, operation, required_successes).await;
        }

        // For QUORUM and ONE, use early termination for better performance
        self.execute_write_with_early_termination(peer_nodes, operation, required_successes).await
    }

    /// Execute write to all nodes (for ALL consistency)
    async fn execute_write_all_nodes(&self, peer_nodes: &[String], operation: WriteOperation, required_successes: usize) -> bool {
        let mut futures = Vec::with_capacity(peer_nodes.len());

        for node_url in peer_nodes {
            let pool = self.peer_redis_pool.clone();
            let op = operation.clone();
            let node_url = node_url.clone();

            futures.push(tokio::spawn(async move {
                pool.execute_write_operation(&node_url, &op).await
            }));
        }

        // Wait for all operations to complete
        let results = join_all(futures).await;

        // Count successful operations
        let mut success_count = 0;
        for (i, result) in results.into_iter().enumerate() {
            match result {
                Ok(success) => {
                    if success {
                        success_count += 1;
                    }
                }
                Err(e) => {
                    error!("Task join error for peer node {}: {}", i, e);
                }
            }
        }

        let consistency_met = success_count >= required_successes;

        if consistency_met {
            debug!("Write consistency met: {}/{} peer nodes succeeded (required: {})",
                   success_count, peer_nodes.len(), required_successes);
        } else {
            error!("Write consistency failed: {}/{} peer nodes succeeded (required: {})",
                   success_count, peer_nodes.len(), required_successes);
        }

        consistency_met
    }

    /// Execute write with early termination (for QUORUM and ONE consistency)
    async fn execute_write_with_early_termination(&self, peer_nodes: &[String], operation: WriteOperation, required_successes: usize) -> bool {
        use tokio::sync::mpsc;
        use std::sync::atomic::{AtomicUsize, Ordering};
        use std::sync::Arc;

        let success_count = Arc::new(AtomicUsize::new(0));
        let failure_count = Arc::new(AtomicUsize::new(0));
        let (tx, mut rx) = mpsc::channel(peer_nodes.len());

        // Start write operations to all nodes
        for node_url in peer_nodes {
            let pool = self.peer_redis_pool.clone();
            let op = operation.clone();
            let node_url = node_url.clone();
            let tx = tx.clone();
            let success_count = success_count.clone();
            let failure_count = failure_count.clone();

            tokio::spawn(async move {
                let result = pool.execute_write_operation(&node_url, &op).await;
                if result {
                    success_count.fetch_add(1, Ordering::Relaxed);
                } else {
                    failure_count.fetch_add(1, Ordering::Relaxed);
                }
                let _ = tx.send((node_url, result)).await;
            });
        }

        // Drop the original sender so the channel can close when all tasks complete
        drop(tx);

        let total_nodes = peer_nodes.len();
        let mut completed_count = 0;

        // Wait for results with early termination
        while let Some((node_url, success)) = rx.recv().await {
            completed_count += 1;

            let current_successes = success_count.load(Ordering::Relaxed);

            // Early success: we have enough successful writes
            if current_successes >= required_successes {
                debug!("Write consistency met early: {}/{} peer nodes succeeded (required: {}, completed: {}/{})",
                       current_successes, total_nodes, required_successes, completed_count, total_nodes);
                return true;
            }

            // Early failure: impossible to reach required successes
            let remaining_nodes = total_nodes - completed_count;
            if current_successes + remaining_nodes < required_successes {
                error!("Write consistency failed early: {}/{} peer nodes succeeded, {} remaining (required: {})",
                       current_successes, total_nodes, remaining_nodes, required_successes);
                return false;
            }

            if success {
                debug!("Write operation succeeded on peer node: {}", node_url);
            } else {
                warn!("Write operation failed on peer node: {}", node_url);
            }
        }

        // All operations completed, check final result
        let final_successes = success_count.load(Ordering::Relaxed);
        let consistency_met = final_successes >= required_successes;

        if consistency_met {
            debug!("Write consistency met: {}/{} peer nodes succeeded (required: {})",
                   final_successes, total_nodes, required_successes);
        } else {
            error!("Write consistency failed: {}/{} peer nodes succeeded (required: {})",
                   final_successes, total_nodes, required_successes);
        }

        consistency_met
    }
}

/// Site replication manager for cross-site data replication
pub struct SiteReplicationManager {
    primary_nodes: Vec<String>,
    failover_nodes: Vec<String>,
    retry_count: u32,
    timeout: Duration,
    // Track which nodes are currently available
    node_availability: HashMap<String, bool>,
    last_health_check: std::time::Instant,
    health_check_interval: Duration,
}

impl SiteReplicationManager {
    pub fn new(
        primary_nodes: Vec<String>,
        failover_nodes: Vec<String>,
        retry_count: u32,
        timeout_ms: u64,
    ) -> Self {
        info!("Creating site replication manager with {} primary nodes and {} failover nodes",
              primary_nodes.len(), failover_nodes.len());

        let mut node_availability = HashMap::new();
        // Initially assume all nodes are available
        for node in &primary_nodes {
            node_availability.insert(node.clone(), true);
        }
        for node in &failover_nodes {
            node_availability.insert(node.clone(), true);
        }

        Self {
            primary_nodes,
            failover_nodes,
            retry_count,
            timeout: Duration::from_millis(timeout_ms),
            node_availability,
            last_health_check: std::time::Instant::now(),
            health_check_interval: Duration::from_secs(30), // Check health every 30 seconds
        }
    }

    /// Get the best available nodes for replication (primary first, then failover)
    pub fn get_available_nodes(&mut self) -> Vec<String> {
        let mut available_nodes = Vec::new();

        // First, try primary nodes
        for node in &self.primary_nodes {
            if self.is_node_available(node) {
                available_nodes.push(node.clone());
            }
        }

        // If no primary nodes are available, try failover nodes
        if available_nodes.is_empty() {
            for node in &self.failover_nodes {
                if self.is_node_available(node) {
                    available_nodes.push(node.clone());
                }
            }
        }

        available_nodes
    }

    /// Check if a node is currently available
    fn is_node_available(&self, node: &str) -> bool {
        self.node_availability.get(node).copied().unwrap_or(true)
    }

    /// Mark a node as unavailable
    pub fn mark_node_unavailable(&mut self, node: &str) {
        warn!("Marking site node as unavailable: {}", node);
        self.node_availability.insert(node.to_string(), false);
    }

    /// Mark a node as available
    pub fn mark_node_available(&mut self, node: &str) {
        debug!("Marking site node as available: {}", node);
        self.node_availability.insert(node.to_string(), true);
    }

    /// Replicate write operations to other sites
    pub async fn replicate_to_sites(&mut self, operations: Vec<WriteOperation>) -> bool {
        if operations.is_empty() {
            return true;
        }

        let available_nodes = self.get_available_nodes();
        if available_nodes.is_empty() {
            error!("No available site nodes for replication");
            return false;
        }

        info!("Replicating {} operations to {} site nodes", operations.len(), available_nodes.len());

        let mut success_count = 0;
        let total_nodes = available_nodes.len();

        // Try to replicate to all available nodes
        for node in available_nodes {
            if self.replicate_to_single_site(&node, &operations).await {
                success_count += 1;
                self.mark_node_available(&node);
            } else {
                self.mark_node_unavailable(&node);
            }
        }

        let success = success_count > 0; // At least one site should succeed
        if success {
            info!("Site replication succeeded: {}/{} nodes", success_count, total_nodes);
        } else {
            error!("Site replication failed: {}/{} nodes", success_count, total_nodes);
        }

        success
    }

    /// Replicate operations to a single site node
    async fn replicate_to_single_site(&self, node_url: &str, operations: &[WriteOperation]) -> bool {
        // This is a placeholder for the actual gRPC client implementation
        // In the actual implementation, we would:
        // 1. Create a gRPC client for the site node
        // 2. Convert WriteOperations to BatchOperations
        // 3. Send BatchWriteRequest with skip_replication=false
        // 4. Handle retries and timeouts

        info!("Replicating {} operations to site node: {}", operations.len(), node_url);

        for attempt in 0..=self.retry_count {
            // Simulate the replication attempt
            // In real implementation, this would be the actual gRPC call
            match self.send_operations_to_site(node_url, operations).await {
                Ok(_) => {
                    if attempt > 0 {
                        debug!("Site replication succeeded to {} after {} retries", node_url, attempt);
                    }
                    return true;
                }
                Err(e) => {
                    if attempt < self.retry_count {
                        warn!("Site replication failed to {} (attempt {}/{}): {}. Retrying...",
                              node_url, attempt + 1, self.retry_count + 1, e);
                        sleep(Duration::from_millis(100 * (attempt + 1) as u64)).await;
                    } else {
                        error!("Site replication failed to {} after {} attempts: {}",
                               node_url, self.retry_count + 1, e);
                    }
                }
            }
        }

        false
    }

    /// Send operations to a site node (placeholder for actual gRPC implementation)
    async fn send_operations_to_site(&self, _node_url: &str, _operations: &[WriteOperation]) -> Result<(), String> {
        // This is a placeholder that will be implemented when integrating with gRPC
        // For now, we'll simulate success/failure
        tokio::time::sleep(Duration::from_millis(10)).await;
        Ok(())
    }

    /// Get statistics about site replication
    pub fn get_site_stats(&self) -> HashMap<String, bool> {
        self.node_availability.clone()
    }
}

use std::collections::HashMap;
use std::sync::Arc;
use std::time::Duration;
use tokio::time::sleep;
use futures::future::join_all;
use log::{debug, error, info, warn};

/// Write consistency levels for synchronous replication
#[derive(Debug, <PERSON>lone, PartialEq)]
pub enum WriteConsistency {
    All,    // All peer nodes must respond successfully
    Quorum, // Majority (or quorum_value) of peer nodes must respond successfully
    One,    // At least one peer node must respond successfully
}

impl WriteConsistency {
    pub fn from_string(s: &str) -> Result<Self, String> {
        match s.to_uppercase().as_str() {
            "ALL" => Ok(WriteConsistency::All),
            "QUORUM" => Ok(WriteConsistency::Quorum),
            "ONE" => Ok(WriteConsistency::One),
            _ => Err(format!("Invalid write consistency: {}. Must be ALL, QUORUM, or ONE", s)),
        }
    }
}

/// Write operation types for peer Redis nodes
#[derive(Debug, <PERSON><PERSON>)]
pub enum WriteOperation {
    Set { key: String, value: String },
    Delete { key: String },
    SetEx { key: String, value: String, ttl: u64 },
    SetExpiry { key: String, ttl: i64 },
    IncrBy { key: String, value: i64 },
    DecrBy { key: String, value: i64 },
    IncrByFloat { key: String, value: f64 },
    HSet { key: String, field: String, value: String },
    HIncrBy { key: String, field: String, value: i64 },
    HDecrBy { key: String, field: String, value: i64 },
    HIncrByFloat { key: String, field: String, value: f64 },
}

/// Redis connection pool for peer nodes
pub struct PeerRedisPool {
    pools: HashMap<String, deadpool_redis::Pool>,
    retry_count: u32,
    retry_delay: Duration,
    pool_size: usize,
}

impl PeerRedisPool {
    pub async fn new(peer_nodes: &[String], pool_size: usize, retry_count: u32, retry_delay_ms: u64) -> Self {
        info!("Creating peer Redis pools with size {} for {} nodes", pool_size, peer_nodes.len());

        let mut pools = HashMap::new();

        for node_url in peer_nodes {
            match deadpool_redis::Manager::new(node_url.clone()) {
                Ok(manager) => {
                    match deadpool_redis::Pool::builder(manager)
                        .max_size(pool_size)
                        .runtime(deadpool_redis::Runtime::Tokio1)
                        .wait_timeout(Some(Duration::from_millis(200))) // Increased from 100ms
                        .create_timeout(Some(Duration::from_secs(3)))   // Increased from 2s
                        .recycle_timeout(Some(Duration::from_secs(120))) // Increased from 60s
                        .build()
                    {
                        Ok(pool) => {
                            pools.insert(node_url.clone(), pool);
                            info!("Created Redis pool for peer node: {} with size {}", node_url, pool_size);
                        }
                        Err(e) => {
                            error!("Failed to create Redis pool for peer node {}: {}", node_url, e);
                        }
                    }
                }
                Err(e) => {
                    error!("Failed to create Redis manager for peer node {}: {}", node_url, e);
                }
            }
        }

        Self {
            pools,
            retry_count,
            retry_delay: Duration::from_millis(retry_delay_ms),
            pool_size,
        }
    }

    pub async fn execute_write_operation(&self, node_url: &str, operation: &WriteOperation) -> bool {
        if let Some(pool) = self.pools.get(node_url) {
            for attempt in 0..=self.retry_count {
                match pool.get().await {
                    Ok(mut conn) => {
                        let result = match operation {
                            WriteOperation::Set { key, value } => {
                                use deadpool_redis::redis::AsyncCommands;
                                conn.set::<_, _, ()>(key, value).await
                            }
                            WriteOperation::Delete { key } => {
                                use deadpool_redis::redis::AsyncCommands;
                                conn.del::<_, ()>(key).await
                            }
                            WriteOperation::SetEx { key, value, ttl } => {
                                use deadpool_redis::redis::AsyncCommands;
                                conn.set_ex::<_, _, ()>(key, value, *ttl).await
                            }
                            WriteOperation::SetExpiry { key, ttl } => {
                                use deadpool_redis::redis::AsyncCommands;
                                conn.expire::<_, ()>(key, *ttl).await
                            }
                            WriteOperation::IncrBy { key, value } => {
                                use deadpool_redis::redis::AsyncCommands;
                                conn.incr::<_, _, ()>(key, *value).await
                            }
                            WriteOperation::DecrBy { key, value } => {
                                use deadpool_redis::redis::AsyncCommands;
                                conn.decr::<_, _, ()>(key, *value).await
                            }
                            WriteOperation::IncrByFloat { key, value } => {
                                use deadpool_redis::redis::cmd;
                                cmd("INCRBYFLOAT")
                                    .arg(key)
                                    .arg(*value)
                                    .query_async(&mut *conn)
                                    .await
                            }
                            WriteOperation::HSet { key, field, value } => {
                                use deadpool_redis::redis::AsyncCommands;
                                conn.hset::<_, _, _, ()>(key, field, value).await
                            }
                            WriteOperation::HIncrBy { key, field, value } => {
                                use deadpool_redis::redis::AsyncCommands;
                                conn.hincr::<_, _, _, ()>(key, field, *value).await
                            }
                            WriteOperation::HDecrBy { key, field, value } => {
                                use deadpool_redis::redis::AsyncCommands;
                                conn.hincr::<_, _, _, ()>(key, field, -*value).await
                            }
                            WriteOperation::HIncrByFloat { key, field, value } => {
                                use deadpool_redis::redis::cmd;
                                cmd("HINCRBYFLOAT")
                                    .arg(key)
                                    .arg(field)
                                    .arg(*value)
                                    .query_async(&mut *conn)
                                    .await
                            }
                        };

                        match result {
                            Ok(_) => {
                                if attempt > 0 {
                                    debug!("Write operation succeeded on peer {} after {} retries", node_url, attempt);
                                }
                                return true;
                            }
                            Err(e) => {
                                if attempt < self.retry_count {
                                    warn!("Write operation failed on peer {} (attempt {}/{}): {}. Retrying...",
                                          node_url, attempt + 1, self.retry_count + 1, e);
                                    sleep(self.retry_delay).await;
                                } else {
                                    error!("Write operation failed on peer {} after {} attempts: {}",
                                           node_url, self.retry_count + 1, e);
                                }
                            }
                        }
                    }
                    Err(e) => {
                        if attempt < self.retry_count {
                            warn!("Failed to get connection to peer {} (attempt {}/{}): {}. Retrying...",
                                  node_url, attempt + 1, self.retry_count + 1, e);
                            sleep(self.retry_delay).await;
                        } else {
                            error!("Failed to get connection to peer {} after {} attempts: {}",
                                   node_url, self.retry_count + 1, e);
                        }
                    }
                }
            }
        } else {
            error!("No Redis pool found for peer node: {}", node_url);
        }
        false
    }

    /// Get pool statistics for monitoring
    pub fn get_pool_stats(&self) -> HashMap<String, (usize, usize)> {
        let mut stats = HashMap::new();
        for (node_url, pool) in &self.pools {
            let status = pool.status();
            stats.insert(
                node_url.clone(),
                (status.size, self.pool_size) // (current_size, max_size)
            );
        }
        stats
    }

    /// Get the configured pool size
    pub fn get_pool_size(&self) -> usize {
        self.pool_size
    }
}

/// Write consistency checker for synchronous replication
pub struct WriteConsistencyChecker {
    peer_redis_pool: Arc<PeerRedisPool>,
    write_consistency: WriteConsistency,
    quorum_value: usize,
}

impl WriteConsistencyChecker {
    pub async fn new(
        peer_nodes: &[String],
        write_consistency: WriteConsistency,
        quorum_value: usize,
        pool_size: usize,
        retry_count: u32,
        retry_delay_ms: u64,
    ) -> Self {
        let peer_redis_pool = Arc::new(
            PeerRedisPool::new(peer_nodes, pool_size, retry_count, retry_delay_ms).await
        );

        Self {
            peer_redis_pool,
            write_consistency,
            quorum_value,
        }
    }

    /// Execute write operation on peer Redis nodes and check consistency
    pub async fn execute_write_with_consistency(&self, peer_nodes: &[String], operation: WriteOperation) -> bool {
        if peer_nodes.is_empty() {
            return true; // No peers to write to, consider it successful
        }

        // Calculate required successes upfront
        let required_successes = match self.write_consistency {
            WriteConsistency::All => peer_nodes.len(),
            WriteConsistency::Quorum => self.quorum_value.min(peer_nodes.len()),
            WriteConsistency::One => 1,
        };

        // For ALL consistency, we must try all nodes
        if matches!(self.write_consistency, WriteConsistency::All) {
            return self.execute_write_all_nodes(peer_nodes, operation, required_successes).await;
        }

        // For QUORUM and ONE, use early termination for better performance
        self.execute_write_with_early_termination(peer_nodes, operation, required_successes).await
    }

    /// Execute write to all nodes (for ALL consistency)
    async fn execute_write_all_nodes(&self, peer_nodes: &[String], operation: WriteOperation, required_successes: usize) -> bool {
        let mut futures = Vec::with_capacity(peer_nodes.len());

        for node_url in peer_nodes {
            let pool = self.peer_redis_pool.clone();
            let op = operation.clone();
            let node_url = node_url.clone();

            futures.push(tokio::spawn(async move {
                pool.execute_write_operation(&node_url, &op).await
            }));
        }

        // Wait for all operations to complete
        let results = join_all(futures).await;

        // Count successful operations
        let mut success_count = 0;
        for (i, result) in results.into_iter().enumerate() {
            match result {
                Ok(success) => {
                    if success {
                        success_count += 1;
                    }
                }
                Err(e) => {
                    error!("Task join error for peer node {}: {}", i, e);
                }
            }
        }

        let consistency_met = success_count >= required_successes;

        if consistency_met {
            debug!("Write consistency met: {}/{} peer nodes succeeded (required: {})",
                   success_count, peer_nodes.len(), required_successes);
        } else {
            error!("Write consistency failed: {}/{} peer nodes succeeded (required: {})",
                   success_count, peer_nodes.len(), required_successes);
        }

        consistency_met
    }

    /// Execute write with early termination (for QUORUM and ONE consistency)
    async fn execute_write_with_early_termination(&self, peer_nodes: &[String], operation: WriteOperation, required_successes: usize) -> bool {
        use tokio::sync::mpsc;
        use std::sync::atomic::{AtomicUsize, Ordering};
        use std::sync::Arc;

        let success_count = Arc::new(AtomicUsize::new(0));
        let failure_count = Arc::new(AtomicUsize::new(0));
        let (tx, mut rx) = mpsc::channel(peer_nodes.len());

        // Start write operations to all nodes
        for node_url in peer_nodes {
            let pool = self.peer_redis_pool.clone();
            let op = operation.clone();
            let node_url = node_url.clone();
            let tx = tx.clone();
            let success_count = success_count.clone();
            let failure_count = failure_count.clone();

            tokio::spawn(async move {
                let result = pool.execute_write_operation(&node_url, &op).await;
                if result {
                    success_count.fetch_add(1, Ordering::Relaxed);
                } else {
                    failure_count.fetch_add(1, Ordering::Relaxed);
                }
                let _ = tx.send((node_url, result)).await;
            });
        }

        // Drop the original sender so the channel can close when all tasks complete
        drop(tx);

        let total_nodes = peer_nodes.len();
        let mut completed_count = 0;

        // Wait for results with early termination
        while let Some((node_url, success)) = rx.recv().await {
            completed_count += 1;

            let current_successes = success_count.load(Ordering::Relaxed);

            // Early success: we have enough successful writes
            if current_successes >= required_successes {
                debug!("Write consistency met early: {}/{} peer nodes succeeded (required: {}, completed: {}/{})",
                       current_successes, total_nodes, required_successes, completed_count, total_nodes);
                return true;
            }

            // Early failure: impossible to reach required successes
            let remaining_nodes = total_nodes - completed_count;
            if current_successes + remaining_nodes < required_successes {
                error!("Write consistency failed early: {}/{} peer nodes succeeded, {} remaining (required: {})",
                       current_successes, total_nodes, remaining_nodes, required_successes);
                return false;
            }

            if success {
                debug!("Write operation succeeded on peer node: {}", node_url);
            } else {
                warn!("Write operation failed on peer node: {}", node_url);
            }
        }

        // All operations completed, check final result
        let final_successes = success_count.load(Ordering::Relaxed);
        let consistency_met = final_successes >= required_successes;

        if consistency_met {
            debug!("Write consistency met: {}/{} peer nodes succeeded (required: {})",
                   final_successes, total_nodes, required_successes);
        } else {
            error!("Write consistency failed: {}/{} peer nodes succeeded (required: {})",
                   final_successes, total_nodes, required_successes);
        }

        consistency_met
    }
}

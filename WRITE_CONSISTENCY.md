# Write Consistency in RustyCluster

This document explains the write consistency feature implemented in RustyCluster for synchronous replication.

## Overview

Write consistency ensures that write operations are successfully replicated to a configurable number of peer Redis nodes before responding to the client. This feature is only active when `async_replication = false`.

## Architecture

The write consistency feature is implemented in a modular way:

- **`src/write_consistency.rs`**: Contains the core write consistency logic
  - `WriteConsistency` enum: Defines consistency levels (ALL, QUORUM, ONE)
  - `WriteOperation` enum: Defines supported write operations
  - `PeerRedisPool`: Manages direct Redis connections to peer nodes
  - `WriteConsistencyChecker`: Orchestrates consistency checking

- **`src/grpc.rs`**: Integrates write consistency into gRPC service methods
- **`src/app_config.rs`**: Configuration management for write consistency
- **`src/main.rs`**: Service initialization with write consistency parameters

## How It Works

### Flow for Synchronous Replication (`async_replication = false`)

1. **Client sends write request** to Rust node
2. **Rust node writes to local Redis** first
3. **Rust node writes to peer Redis nodes** directly (not through other Rust nodes)
4. **Rust node waits for responses** from all peer Redis nodes
5. **Rust node counts successful responses**
6. **Rust node checks write consistency**:
   - `ALL`: Success only if all peer Redis nodes responded successfully
   - `QUORUM`: Success if majority (or `quorum_value`) responded successfully
   - `ONE`: Success if at least one peer Redis node responded successfully
7. **Rust node responds to client** with success/failure based on consistency check

**Important**: When `async_replication = false`, the system:
- Does **NOT** replicate to secondary Rust nodes to ensure immediate client response
- Does **NOT** create connections to secondary Rust nodes (no wasted resources)
- Does **NOT** start background replication tasks or health checks
- **ONLY** uses peer Redis nodes for write consistency (direct Redis connections)

### Flow for Asynchronous Replication (`async_replication = true`)

- Uses existing flow (no write consistency checking)
- Writes to local Redis and responds immediately to client
- Replicates to secondary Rust nodes asynchronously

## Configuration

Add the following configuration to your `config.toml`:

```toml
# IMPORTANT: Set to false to enable write consistency
async_replication = false

# List of peer Redis nodes for write consistency
peer_redis_nodes = ["redis://username:password@host1:port", "redis://username:password@host2:port"]

# Write consistency level: ALL, QUORUM, or ONE
write_consistency = "QUORUM"

# Number of nodes expected for quorum (used when write_consistency = "QUORUM")
quorum_value = 2

# Number of retry attempts for write consistency operations
write_retry_count = 3

# Connection pool size for each peer Redis node
peer_redis_pool_size = 64
```

### Configuration Parameters

- **`peer_redis_nodes`**: List of Redis URLs for peer nodes (direct Redis connections)
- **`write_consistency`**: Consistency level - "ALL", "QUORUM", or "ONE"
- **`quorum_value`**: Number of nodes required for QUORUM consistency
- **`write_retry_count`**: Number of retry attempts for failed write operations
- **`peer_redis_pool_size`**: Connection pool size for each peer Redis node (default: 64)

### Consistency Levels

- **ALL**: All peer Redis nodes must respond successfully
- **QUORUM**: At least `quorum_value` peer Redis nodes must respond successfully
- **ONE**: At least one peer Redis node must respond successfully

## Error Handling

- **Retry Logic**: Failed write operations are retried up to `write_retry_count` times
- **Error Logging**: Failed writes are logged as errors with detailed information
- **Client Response**: If write consistency fails, client receives an error response

## Performance Considerations

- **Latency**: Write consistency adds latency as it waits for peer Redis responses
- **Early Termination Optimization**:
  - `ONE` and `QUORUM`: Stop as soon as required successes are achieved
  - `ALL`: Must wait for all nodes (no early termination possible)
  - Significantly faster response times when some nodes are down
- **Connection Pooling**: Efficient Redis connection pools for peer nodes
  - Pool size directly configurable via `peer_redis_pool_size` parameter
  - Validated range: 1-2048 connections per peer node (default: 64)
  - Optimized timeouts: 200ms wait, 3s create, 120s recycle
- **Consistency Levels**:
  - Use `ONE` consistency for better performance with basic redundancy
  - Use `QUORUM` for balanced consistency and availability
  - Use `ALL` for maximum consistency but highest latency
- **Monitoring**: Pool statistics and performance metrics available

## Example Usage

1. **Start Redis servers** for peer nodes:
```bash
redis-server --port 6370 --requirepass "your_password"
redis-server --port 6371 --requirepass "your_password"
```

2. **Configure RustyCluster** with write consistency:
```bash
cargo run --release config_write_consistency_test.toml
```

3. **Test write operations** - they will now enforce consistency across peer Redis nodes.

## Monitoring

Write consistency operations are logged with the following information:
- Success/failure of individual peer node writes
- Overall consistency check results
- Retry attempts and failures
- Performance metrics (response times)

Check the logs for messages like:
- "Write consistency met: X/Y peer nodes succeeded"
- "Write consistency failed: X/Y peer nodes succeeded"
- "Write operation failed on peer {node} after {retries} attempts"

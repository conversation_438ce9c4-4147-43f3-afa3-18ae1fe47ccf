# Write Consistency and Site Replication in RustyCluster

This document explains the write consistency and site replication features implemented in RustyCluster for synchronous replication and cross-site data replication.

## Overview

Write consistency ensures that write operations are successfully replicated to a configurable number of peer Redis nodes before responding to the client. This feature is only active when `async_replication = false`.

Site replication enables cross-site data replication by sending write operations to primary nodes in other sites with `skip_replication = false`, allowing those sites to replicate the data to their own nodes. This provides disaster recovery and geographic distribution capabilities.

## Architecture

The write consistency feature is implemented in a modular way:

- **`src/write_consistency.rs`**: Contains the core write consistency and site replication logic
  - `WriteConsistency` enum: Defines consistency levels (ALL, QUORUM, ONE)
  - `WriteOperation` enum: Defines supported write operations
  - `PeerRedisPool`: Manages direct Redis connections to peer nodes
  - `WriteConsistencyChecker`: Orchestrates consistency checking
  - `SiteReplicationManager`: Manages cross-site replication with failover support

- **`src/grpc.rs`**: Integrates write consistency into gRPC service methods
- **`src/app_config.rs`**: Configuration management for write consistency
- **`src/main.rs`**: Service initialization with write consistency parameters

## How It Works

### Flow for Synchronous Replication (`async_replication = false`)

1. **Client sends write request** to Rust node
2. **Rust node writes to local Redis** first
3. **Rust node writes to peer Redis nodes** directly (not through other Rust nodes)
4. **Rust node waits for responses** from all peer Redis nodes
5. **Rust node counts successful responses**
6. **Rust node checks write consistency**:
   - `ALL`: Success only if all peer Redis nodes responded successfully
   - `QUORUM`: Success if majority (or `quorum_value`) responded successfully
   - `ONE`: Success if at least one peer Redis node responded successfully
7. **Rust node responds to client** with success/failure based on consistency check

**Important**: When `async_replication = false`, the system:
- Does **NOT** replicate to secondary Rust nodes to ensure immediate client response
- Does **NOT** create connections to secondary Rust nodes (no wasted resources)
- Does **NOT** start background replication tasks or health checks
- **ONLY** uses peer Redis nodes for write consistency (direct Redis connections)

### Flow for Asynchronous Replication (`async_replication = true`)

- Uses existing flow (no write consistency checking)
- Writes to local Redis and responds immediately to client
- Replicates to secondary Rust nodes asynchronously

## Site Replication

Site replication provides cross-site data replication capabilities for disaster recovery and geographic distribution.

### How Site Replication Works

1. **Client sends write request** to local Rust node
2. **Local Rust node writes to local Redis** first
3. **Local Rust node performs write consistency** (if enabled) to peer Redis nodes
4. **Local Rust node replicates to other sites** by sending gRPC requests to primary nodes in other sites
5. **Site replication uses `skip_replication = false`** so remote sites replicate to their own nodes
6. **Failover support**: If primary nodes are unavailable, failover nodes are used automatically
7. **Local Rust node responds to client** after local operations complete (site replication doesn't block response)

### Site Replication Features

- **Primary/Failover Configuration**: Configure primary nodes for each site with failover nodes as backup
- **Automatic Failover**: Automatically switches to failover nodes when primary nodes are unavailable
- **Non-blocking**: Site replication failures don't block client responses (local write still succeeds)
- **Retry Logic**: Configurable retry attempts with exponential backoff for site replication
- **Health Tracking**: Tracks availability of site nodes and marks them as available/unavailable

## Configuration

Add the following configuration to your `config.toml`:

```toml
# IMPORTANT: Set to false to enable write consistency
async_replication = false

# List of peer Redis nodes for write consistency
peer_redis_nodes = ["redis://username:password@host1:port", "redis://username:password@host2:port"]

# Write consistency level: ALL, QUORUM, or ONE
write_consistency = "QUORUM"

# Number of nodes expected for quorum (used when write_consistency = "QUORUM")
quorum_value = 2

# Number of retry attempts for write consistency operations
write_retry_count = 3

# Connection pool size for each peer Redis node
peer_redis_pool_size = 64

# Site replication configuration
site_replication_enabled = true
site_primary_nodes = ["http://site2-primary:50051", "http://site3-primary:50051"]
site_failover_nodes = ["http://site2-failover:50051", "http://site3-failover:50051"]
site_replication_retry_count = 3
site_replication_timeout_ms = 5000
```

### Configuration Parameters

- **`peer_redis_nodes`**: List of Redis URLs for peer nodes (direct Redis connections)
- **`write_consistency`**: Consistency level - "ALL", "QUORUM", or "ONE"
- **`quorum_value`**: Number of nodes required for QUORUM consistency
- **`write_retry_count`**: Number of retry attempts for failed write operations
- **`peer_redis_pool_size`**: Connection pool size for each peer Redis node (default: 64)

#### Site Replication Parameters

- **`site_replication_enabled`**: Enable/disable site replication (default: false)
- **`site_primary_nodes`**: List of gRPC URLs for primary nodes in other sites
- **`site_failover_nodes`**: List of gRPC URLs for failover nodes in other sites
- **`site_replication_retry_count`**: Number of retry attempts for site replication (default: 3)
- **`site_replication_timeout_ms`**: Timeout for site replication operations in milliseconds (default: 5000)

### Consistency Levels

- **ALL**: All peer Redis nodes must respond successfully
- **QUORUM**: At least `quorum_value` peer Redis nodes must respond successfully
- **ONE**: At least one peer Redis node must respond successfully

## Error Handling

- **Retry Logic**: Failed write operations are retried up to `write_retry_count` times
- **Error Logging**: Failed writes are logged as errors with detailed information
- **Client Response**: If write consistency fails, client receives an error response

## Performance Considerations

- **Latency**: Write consistency adds latency as it waits for peer Redis responses
- **Early Termination Optimization**:
  - `ONE` and `QUORUM`: Stop as soon as required successes are achieved
  - `ALL`: Must wait for all nodes (no early termination possible)
  - Significantly faster response times when some nodes are down
- **Connection Pooling**: Efficient Redis connection pools for peer nodes
  - Pool size directly configurable via `peer_redis_pool_size` parameter
  - Validated range: 1-2048 connections per peer node (default: 64)
  - Optimized timeouts: 200ms wait, 3s create, 120s recycle
- **Consistency Levels**:
  - Use `ONE` consistency for better performance with basic redundancy
  - Use `QUORUM` for balanced consistency and availability
  - Use `ALL` for maximum consistency but highest latency
- **Monitoring**: Pool statistics and performance metrics available

## Example Usage

1. **Start Redis servers** for peer nodes:
```bash
redis-server --port 6370 --requirepass "your_password"
redis-server --port 6371 --requirepass "your_password"
```

2. **Configure RustyCluster** with write consistency:
```bash
cargo run --release config_write_consistency_test.toml
```

3. **Test write operations** - they will now enforce consistency across peer Redis nodes.

### Site Replication Example

1. **Start Redis servers** for each site:
```bash
# Site 1 (local)
redis-server --port 6379 --requirepass "your_password"
redis-server --port 6370 --requirepass "your_password"  # peer node
redis-server --port 6371 --requirepass "your_password"  # peer node

# Site 2 (remote)
redis-server --port 6379 --requirepass "your_password"
redis-server --port 6370 --requirepass "your_password"  # peer node
redis-server --port 6371 --requirepass "your_password"  # peer node
```

2. **Start RustyCluster nodes** for each site:
```bash
# Site 1 - Primary node
cargo run --release config_site_replication_test.toml

# Site 2 - Primary node (on different server)
cargo run --release config_site2_primary.toml

# Site 2 - Failover node (on different server)
cargo run --release config_site2_failover.toml
```

3. **Test cross-site replication** - write operations will be replicated to other sites automatically.

## Monitoring

Write consistency operations are logged with the following information:
- Success/failure of individual peer node writes
- Overall consistency check results
- Retry attempts and failures
- Performance metrics (response times)

Check the logs for messages like:
- "Write consistency met: X/Y peer nodes succeeded"
- "Write consistency failed: X/Y peer nodes succeeded"
- "Write operation failed on peer {node} after {retries} attempts"

### Site Replication Monitoring

Site replication operations are logged with the following information:
- Success/failure of site replication operations
- Node availability and failover events
- Retry attempts and failures
- Performance metrics for cross-site operations

Check the logs for messages like:
- "Site replication succeeded: X/Y nodes"
- "Site replication failed: X/Y nodes"
- "Marking site node as unavailable: {node}"
- "Site replication failed for SET operation on key: {key}"

mod redis_lib;
mod app_config;
mod grpc;
mod tracing_config;
mod auth;
mod write_consistency;

use redis_lib::RedisClient;
use app_config::Config;
use tracing_config::{TracingParams, setup_tracing};
use auth::AuthService;
use std::env;
use tracing::{info, error, instrument};
use std::net::SocketAddr;
use tonic::transport::Server;
use crate::grpc::KeyValueServiceImpl;
use crate::write_consistency::WriteConsistency;
use std::path::Path;
use std::sync::Arc;
use std::time::Duration;

#[instrument(name = "main", skip_all)]
fn main() -> Result<(), Box<dyn std::error::Error>> {
    let args: Vec<String> = env::args().collect();

    // Get config file paths from arguments or use defaults
    let config_file = args.get(1).map(|s| s.as_str()).unwrap_or("config.toml");
    let log_config_file = args.get(2).map(|s| s.as_str()).unwrap_or("logconfig.toml");

    // Check if config files exist in current path
    // We need to use eprintln! here since logging is not yet initialized
    if !Path::new(config_file).exists() {
        eprintln!("Configuration file '{}' not found in current directory", config_file);
        std::process::exit(1);
    }

    if !Path::new(log_config_file).exists() {
        eprintln!("Log configuration file '{}' not found, using default logging settings", log_config_file);
    }

    // Load tracing configuration from TOML file
    let tracing_params = TracingParams::from_file(Some(log_config_file))?;

    // Set up the tracing subscriber
    setup_tracing(&tracing_params)?;

    // Now we can use tracing macros
    info!("Tracing initialized with level: {}", tracing_params.level);
    // Load configuration using the `from_file` method
    let settings = Config::from_file(Some(config_file));
    // Configure the tokio runtime with highly optimized settings for maximum throughput
    let runtime = tokio::runtime::Builder::new_multi_thread()
        .worker_threads(settings.worker_threads) // Use configured worker threads
        .max_blocking_threads(settings.worker_threads * 8) // Increased from 4x to 8x for more blocking capacity
        .thread_name("rustycluster-worker") // Name threads for better debugging
        .thread_stack_size(4 * 1024 * 1024) // Increased from 3MB to 4MB stack size
        .global_queue_interval(61) // Optimize work stealing (prime number)
        .event_interval(11) // Optimize event processing (prime number)
        .enable_all() // Enable all features (IO, time, etc.)
        .build()?;
    runtime.block_on(async move {
    // Create a span for the server startup process
    let _server_span = tracing::info_span!("server_startup",
        version = env!("CARGO_PKG_VERSION"),
        worker_threads = settings.worker_threads
    ).entered();

    info!("Starting RustyCluster server...");

    let redis_url = settings.redis_url;
    let port = settings.port;
    let secondary_nodes = settings.secondary_nodes.clone();
    let replication_factor = settings.replication_factor;
    let read_consistency = settings.read_consistency;
    let async_replication = settings.async_replication;
    let redis_pool_size = settings.redis_pool_size;
    let max_retries = settings.max_retries;
    let retry_delay_ms = settings.retry_delay_ms;
    let replication_batch_max_age_secs = settings.replication_batch_max_age_secs;
    let secondary_pool_size = settings.secondary_pool_size;
    let max_batch_size = settings.max_batch_size;
    let batch_flush_interval_ms = settings.batch_flush_interval_ms;

    // Add structured fields to the current span
    tracing::Span::current().record("redis_url", &redis_url);
    tracing::Span::current().record("port", &port);
    tracing::Span::current().record("replication_factor", &replication_factor);
    tracing::Span::current().record("secondary_nodes_count", &secondary_nodes.len());

    let redis_client = RedisClient::new(&redis_url, redis_pool_size)?;

    // Test Redis connection in a new span
    {
        let redis_span = tracing::info_span!("redis_connection_test", redis_url = %redis_url).entered();

        if let Err(err) = redis_client.ping().await {
            error!(error = %err, "Failed to connect to Redis");
            std::process::exit(1);
        }

        info!("Successfully connected to Redis");
        drop(redis_span); // Explicitly drop the span
    }

    let addr = SocketAddr::from(([0, 0, 0, 0], port));
    info!(address = %addr, "Server listening");

    // Initialize authentication service
    let auth_service = Arc::new(
        AuthService::new(
            settings.auth_enabled,
            String::new(), // No token-based auth for now
            settings.auth_username.clone(),
            settings.auth_password.clone(),
        ).with_session_duration(Duration::from_secs(settings.session_duration_secs))
    );

    // Start the session cleanup task
    if settings.auth_enabled {
        info!("Authentication enabled with username: {}", settings.auth_username);
        auth_service.clone().start_cleanup_task();
    } else {
        info!("Authentication disabled");
    }

    // Parse write consistency configuration
    let write_consistency = if !settings.peer_redis_nodes.is_empty() && !settings.write_consistency.is_empty() {
        match WriteConsistency::from_string(&settings.write_consistency) {
            Ok(consistency) => Some(consistency),
            Err(e) => {
                error!("Invalid write consistency configuration: {}", e);
                std::process::exit(1);
            }
        }
    } else {
        None
    };

    let service = KeyValueServiceImpl::new(
        redis_client,
        secondary_nodes,
        replication_factor,
        read_consistency,
        async_replication,
        max_retries,
        retry_delay_ms,
        replication_batch_max_age_secs,
        secondary_pool_size,
        max_batch_size,
        batch_flush_interval_ms,
        settings.chunk_size,
        settings.num_shards,
        auth_service.clone(),
        // Write consistency parameters
        settings.peer_redis_nodes.clone(),
        write_consistency,
        settings.quorum_value,
        settings.write_retry_count,
        settings.peer_redis_pool_size,
    ).await;

    // Create a span for server configuration
    let server_config_span = tracing::info_span!("server_configuration",
        tcp_keepalive_secs = settings.tcp_keepalive_secs,
        tcp_nodelay = settings.tcp_nodelay,
        concurrency_limit = settings.concurrency_limit,
        max_concurrent_streams = settings.max_concurrent_streams
    ).entered();

    info!("Configuring server with optimized settings");

    // Configure the server with highly optimized settings for maximum throughput
    let mut server = Server::builder()
        // TCP-level optimizations
        .tcp_keepalive(Some(std::time::Duration::from_secs(settings.tcp_keepalive_secs)))
        .tcp_nodelay(settings.tcp_nodelay) // Disable Nagle's algorithm for better latency

        // HTTP/2 optimizations
        .concurrency_limit_per_connection(settings.concurrency_limit)
        .max_concurrent_streams(settings.max_concurrent_streams)
        .initial_connection_window_size(1024 * 1024 * 32) // Increased from 16MB to 32MB
        .initial_stream_window_size(1024 * 1024 * 16) // Increased from 8MB to 16MB
        .http2_keepalive_interval(Some(std::time::Duration::from_secs(3))) // Reduced from 5s to 3s
        .http2_keepalive_timeout(Some(std::time::Duration::from_secs(5))) // Reduced from 10s to 5s
        .http2_adaptive_window(Some(true)) // Enable adaptive flow control
        .timeout(std::time::Duration::from_secs(60)); // Add a reasonable timeout

    drop(server_config_span);

    // Create a span for the server lifecycle
    let server_span = tracing::info_span!("server_lifecycle", address = %addr).entered();

    // Add our service and start serving
    info!("Starting gRPC server");
    server.add_service(service.into_server())
          .serve(addr)
          .await?;

    drop(server_span);
    Ok(())
})
}
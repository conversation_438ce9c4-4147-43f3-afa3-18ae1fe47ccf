[package]
name = "rustycluster"
version = "2.2.0"
edition = "2024"

[dependencies]
# Logging and tracing
log = "0.4"  # Keep for compatibility with dependencies that use log
tracing = "0.1"  # Core tracing functionality
tracing-subscriber = { version = "0.3", features = ["env-filter", "json", "registry", "chrono"] }
tracing-appender = "0.2"  # For file logging with rotation
tracing-log = "0.1"  # Bridge from log to tracing
console-subscriber = { version = "0.1", optional = true }  # For Tokio Console
lazy_static = "1.4"  # For global state management

# Redis and HTTP
deadpool-redis = "0.20"  # Upgrade from 0.14
redis = { version = "0.31", features = ["aio", "tokio-comp"] }  # Upgrade from 0.24
tokio = { version = "1.38", features = ["full", "tracing"] }  # Added tracing feature

# Configuration and serialization
config = "0.13"
serde = { version = "1.0", features = ["derive"] }
futures = "0.3"
prometheus = "0.13"

# gRPC
tonic = "0.13"
prost = "0.13"
prost-types = "0.13"

# Other
num_cpus = "1.16"
parking_lot = "0.12"  # Efficient mutex implementation
uuid = { version = "1.16.0", features = ["v4"] }
dashmap = "6.1.0"

[features]
default = []
console = ["console-subscriber"]

[build-dependencies]
tonic-build = "0.13"

# RustyCluster

RustyCluster is a distributed key-value store built in Rust, providing high availability and data consistency through replication. It uses Redis as the underlying storage engine and gRPC for inter-node communication.

## Features

- **Distributed Architecture**: Multiple nodes working together to provide high availability
- **Data Replication**: Automatic replication of data to secondary nodes
- **Configurable Consistency**: Adjustable read consistency levels
- **Connection Pooling**: Efficient connection management for both Redis and inter-node communication
- **Asynchronous Operations**: Support for both synchronous and asynchronous replication
- **Rich Data Types**: Support for strings, hashes, and numeric operations
- **TTL Support**: Built-in support for key expiration

## Architecture

RustyCluster consists of multiple nodes, where:
- Each node can be either a primary or secondary node
- Primary nodes handle client requests and replicate data to secondary nodes
- Secondary nodes maintain copies of the data and can serve read requests
- All nodes communicate via gRPC
- Redis is used as the underlying storage engine

## Prerequisites

- Rust (latest stable version)
- Redis server
- Protobuf compiler (for gRPC)

## Installation

1. Clone the repository:
```bash
git clone https://github.com/yourusername/rustycluster.git
cd rustycluster
```

2. Build the project:
```bash
cargo build --release
```

## Configuration

RustyCluster uses TOML configuration files. A sample configuration file (`config.toml`) looks like this:

```toml
# Redis connection URL for the primary node
# For Redis without authentication:
redis_url = "redis://127.0.0.1:6379"
# For Redis with authentication (replace 'your_password' with your actual password):
# redis_url = "redis://:your_password@127.0.0.1:6379"

# List of secondary node URLs that this node will replicate to
secondary_nodes = ["http://127.0.0.1:50052", "http://127.0.0.1:50053"]

# Port number on which the gRPC server will listen
port = 50051

# Maximum number of connections in the Redis connection pool
redis_pool_size = 512

# Number of secondary nodes to which data should be replicated
replication_factor = 2

# Number of secondary nodes that must have the data before considering it consistent
read_consistency = 0

# If true, replication to secondary nodes happens asynchronously
async_replication = true

# Maximum number of retry attempts when connecting to secondary nodes
max_retries = 3

# Delay in milliseconds between retry attempts
retry_delay_ms = 100

# Maximum age in seconds for a replication batch
replication_batch_max_age_secs = 30

# Maximum number of connections per secondary node in the gRPC client pool
secondary_pool_size = 512
```

## Client Authentication

RustyCluster supports client authentication with username and password. When enabled, clients must authenticate once during connection initialization before performing any operations.

### Authentication Configuration

Add the following settings to your `config.toml` file:

```toml
# Authentication configuration
auth_enabled = true
auth_username = "your_username"
auth_password = "your_password"
session_duration_secs = 3600  # Session timeout in seconds (1 hour)
```

### Authentication Flow

1. **Connection Initialization**: Client connects to RustyCluster
2. **Authentication**: Client calls the `Authenticate` RPC with username and password
3. **Session Token**: Server returns a session token upon successful authentication
4. **Operations**: Client includes the session token in the `authorization` header for all subsequent operations

### Authentication Notes

- Authentication happens only once during connection initialization
- No authentication is required on each individual operation after the initial authentication
- Session tokens expire after the configured duration (default: 1 hour)
- Authentication is not required between RustyCluster nodes (inter-node communication)
- When `auth_enabled = false`, all requests are allowed without authentication

### Example Usage

1. **Start RustyCluster with authentication enabled**:
```bash
cargo run --release config_auth_test.toml
```

2. **Client Authentication Flow**:
```protobuf
// First, authenticate to get a session token
AuthenticateRequest {
  username: "testuser"
  password: "testpass"
}

// Server responds with:
AuthenticateResponse {
  success: true
  session_token: "uuid-session-token"
  message: "Authentication successful"
}

// Use the session token in subsequent requests
// Add to gRPC metadata: authorization: "Bearer uuid-session-token"
```

3. **All subsequent operations** (Set, Get, Delete, etc.) must include the session token in the authorization header.

### Load Testing with Authentication

For load testing with tools like `ghz`, you have several options:

#### Option 1: Disable Authentication (Recommended for Load Testing)
```bash
# Use config_loadtest.toml with auth_enabled = false
cargo run --release config_loadtest.toml

# Run your existing ghz command
ghz.exe --insecure --proto=../rustycluster.proto --call=rustycluster.KeyValueService.Set --data-file=../set-data.json --rps 10000 -n 200000 -c 1000 127.0.0.1:50051
```

#### Option 2: Use ghz with Session Token
```bash
# Get session token
python get_session_token.py --username testuser --password testpass

# Use token with ghz (replace YOUR_SESSION_TOKEN)
ghz.exe --insecure --proto=../rustycluster.proto --call=rustycluster.KeyValueService.Set --data-file=../set-data.json --metadata="authorization:Bearer YOUR_SESSION_TOKEN" --rps 10000 -n 200000 -c 1000 127.0.0.1:50051
```

#### Option 3: Automated Script
```bash
# Make script executable
chmod +x ghz_with_auth.sh

# Run with authentication
./ghz_with_auth.sh --username testuser --password testpass --rps 10000 -n 200000 -c 1000

# Run without authentication
./ghz_with_auth.sh --no-auth --rps 10000 -n 200000 -c 1000
```

#### Option 4: Windows Batch Script
```cmd
# Get session token (Windows)
get_token.bat --username testuser --password testpass

# Use the displayed token with ghz
ghz.exe --insecure --proto=../rustycluster.proto --call=rustycluster.KeyValueService.Set --data-file=../set-data.json --metadata="authorization:Bearer YOUR_TOKEN" --rps 10000 -n 200000 -c 1000 127.0.0.1:50051
```

📖 **For detailed load testing instructions with authentication, see [LOAD_TEST_WITH_AUTH.md](LOAD_TEST_WITH_AUTH.md)**

### Java Client Example

```java
// Java client authentication example
import io.grpc.ManagedChannel;
import io.grpc.ManagedChannelBuilder;
import io.grpc.Metadata;
import io.grpc.stub.MetadataUtils;

// Create channel
ManagedChannel channel = ManagedChannelBuilder.forAddress("localhost", 50051)
    .usePlaintext()
    .build();

KeyValueServiceGrpc.KeyValueServiceBlockingStub stub =
    KeyValueServiceGrpc.newBlockingStub(channel);

// Authenticate
AuthenticateRequest authRequest = AuthenticateRequest.newBuilder()
    .setUsername("testuser")
    .setPassword("testpass")
    .build();

AuthenticateResponse authResponse = stub.authenticate(authRequest);

if (authResponse.getSuccess()) {
    String sessionToken = authResponse.getSessionToken();

    // Create metadata with session token
    Metadata metadata = new Metadata();
    Metadata.Key<String> authKey = Metadata.Key.of("authorization",
        Metadata.ASCII_STRING_MARSHALLER);
    metadata.put(authKey, "Bearer " + sessionToken);

    // Use authenticated stub for operations
    KeyValueServiceGrpc.KeyValueServiceBlockingStub authenticatedStub =
        MetadataUtils.attachHeaders(stub, metadata);

    // Now you can use authenticatedStub for all operations
    PingResponse pingResponse = authenticatedStub.ping(PingRequest.newBuilder().build());
}
```

## Redis Authentication

RustyCluster supports Redis servers with various authentication methods. Configure your Redis connection in the `config.toml` file using one of the following formats:

### Authentication Options

1. **Username and Password Authentication** (recommended for Redis 6.0+):
```toml
# For Redis with both username and password
redis_url = "redis://username:password@127.0.0.1:6379"
```

2. **Password-only Authentication**:
```toml
# For Redis with password only (no username)
redis_url = "redis://:password@127.0.0.1:6379"
```

3. **Username-only Authentication**:
```toml
# For Redis with username only (treated as password)
redis_url = "redis://username@127.0.0.1:6379"
```

4. **No Authentication**:
```toml
# For Redis without authentication
redis_url = "redis://127.0.0.1:6379"
```

### Authentication Notes

- Redis 6.0+ supports ACL with username and password authentication
- For Redis versions before 6.0, use the password-only format
- When using username-only format (`redis://username@host:port`), Redis treats the username as a password
- For security, RustyCluster logs Redis URLs without exposing credentials

## Running the Application

1. Start Redis servers for each node:
```bash
# Without authentication
redis-server --port 6379  # For primary node
redis-server --port 6370  # For secondary node 1
redis-server --port 6371  # For secondary node 2

# With authentication (example)
redis-server --port 6379 --requirepass "your_password"  # For primary node
redis-server --port 6370 --requirepass "your_password"  # For secondary node 1
redis-server --port 6371 --requirepass "your_password"  # For secondary node 2
```

2. Start the nodes with their respective configuration files:
```bash
# Start primary node
cargo run --release config.toml

# Start secondary nodes
cargo run --release config_node2.toml
cargo run --release config_node3.toml
```

## API Reference

### Authentication Operations
- `Authenticate(username, password)`: Authenticate client and receive session token

### String Operations
- `Set(key, value)`: Set a key-value pair
- `Get(key)`: Retrieve a value by key
- `Delete(key)`: Delete a key
- `SetEx(key, value, ttl)`: Set a key-value pair with expiration
- `SetExpiry(key, ttl)`: Set expiration for an existing key

### Numeric Operations
- `IncrBy(key, value)`: Increment a numeric value
- `DecrBy(key, value)`: Decrement a numeric value
- `IncrByFloat(key, value)`: Increment a floating-point value

### Hash Operations
- `HSet(key, field, value)`: Set a field in a hash
- `HGet(key, field)`: Get a field from a hash
- `HGetAll(key)`: Get all fields from a hash
- `HIncrBy(key, field, value)`: Increment a numeric field
- `HDecrBy(key, field, value)`: Decrement a numeric field
- `HIncrByFloat(key, field, value)`: Increment a floating-point field

## Performance Considerations

- **Connection Pooling**: The application uses connection pooling for both Redis and inter-node communication to improve performance
- **Asynchronous Replication**: When enabled, replication happens asynchronously to avoid blocking client operations
- **Batch Processing**: Operations are batched for efficient replication
- **Configurable Pool Sizes**: Both Redis and secondary node connection pool sizes can be configured based on your needs

## Error Handling

- Automatic retries for failed operations
- Configurable retry count and delay
- Detailed error logging
- Graceful handling of node failures

## Monitoring and Tracing

RustyCluster uses the `tracing` crate for structured logging and diagnostics. This provides several benefits:

- **Structured logging**: Better organization of log data with contextual information
- **Spans**: Track operations across asynchronous boundaries
- **Performance**: More efficient logging with less overhead
- **Integration with Tokio**: Better integration with the Tokio ecosystem

### Configuration

Tracing is configured using a TOML file (default: `logconfig.toml`):

```toml
# Tracing configuration file for RustyCluster
file = "rustycluster.log"
max_size = 10485760  # 10MB
max_files = 5
level = "info"
pattern = "{d(%Y-%m-%d %H:%M:%S%.3f)} - {l} - {M} - {m}\n"
console_enabled = false
```

### Environment Variables

You can override the log level using the `RUST_LOG` environment variable:

```bash
RUST_LOG=debug cargo run --release config.toml
```

### Tokio Console Integration

RustyCluster supports [Tokio Console](https://github.com/tokio-rs/console) for real-time profiling and debugging. To enable it:

1. Build with the console feature:
```bash
cargo build --release --features console
```

2. Set `console_enabled = true` in your `logconfig.toml` file

3. Run with the `tokio_unstable` flag:
```bash
RUSTFLAGS="--cfg tokio_unstable" cargo run --release --features console config.toml
```

4. In another terminal, run the Tokio Console:
```bash
cargo install --locked tokio-console
tokio-console
```

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## License

This project is licensed under the MIT License - see the LICENSE file for details.
# Redis connection URL for the primary node
redis_url = "redis://settlenxt:npci@127.0.0.1:6379"

# List of secondary node URLs that this node will replicate to
secondary_nodes = ["http://127.0.0.1:50052", "http://127.0.0.1:50053"]

# Port number on which the gRPC server will listen
port = 50051

# Maximum number of connections in the Redis connection pool
# Increased to match expected load and reduce connection creation overhead
redis_pool_size = 1024  # Increased from 1024 for even better concurrency

# Number of secondary nodes to which data should be replicated
# 0 means no replication, maximum is the number of secondary nodes
replication_factor = 2

# Number of secondary nodes that must have the data before considering it consistent
# 0 means no consistency check, maximum is the number of secondary nodes
read_consistency = 0

# If true, replication to secondary nodes happens asynchronously
# If false, replication happens synchronously and blocks until complete
# Async replication provides better throughput by not blocking client operations
async_replication = true

# Maximum number of retry attempts when connecting to secondary nodes
# Increased to improve resilience to transient network issues
max_retries = 3  # Increased from 3 for better resilience

# Delay in milliseconds between retry attempts when connecting to secondary nodes
# Reduced for faster recovery from failures
retry_delay_ms = 10  # Reduced from 25ms for even faster retries

# Maximum age in seconds for a replication batch before it's considered expired
# Expired batches are skipped to prevent stale data replication
# Increased to handle larger batches and network delays
replication_batch_max_age_secs = 120  # Increased from 60s to allow more time for large batch replication

# Maximum number of connections per secondary node in the gRPC client pool
# Increased to handle more concurrent replication operations
secondary_pool_size = 1024  # Increased from 512 for better concurrency

# Maximum number of operations in a batch before it's flushed
# Larger batches improve throughput by amortizing overhead across more operations
max_batch_size = 50000  # Increased from 50000 for better batching efficiency

# Interval in milliseconds to flush batches
# Reduced to ensure timely replication even when batch size isn't reached
batch_flush_interval_ms = 10  # Reduced from 20ms for more frequent flushing

# Server configuration
# Sends periodic keepalive probes to maintain connection health
# Reduced to detect connection issues faster
tcp_keepalive_secs = 5  # Reduced from 10s for even faster connection health checks

# Disables Nagle's algorithm when set to true
# This reduces latency by sending packets immediately without waiting to fill buffers
tcp_nodelay = true

# Limits the number of concurrent requests per connection
# Increased to handle more concurrent client requests
concurrency_limit = 1024  # Increased from 1024 for better concurrency

# Controls maximum number of concurrent HTTP/2 streams per connection
# Increased to allow more parallel operations per connection
max_concurrent_streams = 8192  # Increased from 8192 for better parallelism

# Replication chunk size for batch processing
# Increased for more efficient batch processing of large datasets
chunk_size = 10000  # Increased from 10000 for better batching efficiency

# Number of shards for batch collectors
# Increased to reduce lock contention in high-concurrency scenarios
num_shards = 128  # Increased from 128 to further reduce mutex contention

# Configure worker threads based on the CPU size
# Set to a power of 2 for better scheduling efficiency
worker_threads = 32  # Increased from 16 for better parallelism

# Authentication configuration
# Set to true to enable client authentication
auth_enabled = true

# Username for client authentication (required if auth_enabled = true)
auth_username = "admin"

# Password for client authentication (required if auth_enabled = true)
auth_password = "npci"

# Session duration in seconds (default: 3600 = 1 hour)
session_duration_secs = 3600

# Write consistency configuration for synchronous replication
# List of peer Redis nodes for write consistency (when async_replication = false)
# These are direct Redis connections, not Rust nodes
peer_redis_nodes = ["redis://settlenxt:npci@127.0.0.1:6370", "redis://settlenxt:npci@127.0.0.1:6371"]

# Write consistency level: ALL, QUORUM, or ONE
# ALL: All peer Redis nodes must respond successfully
# QUORUM: Majority (or quorum_value) of peer Redis nodes must respond successfully
# ONE: At least one peer Redis node must respond successfully
write_consistency = "QUORUM"

# Number of nodes expected for quorum (used when write_consistency = "QUORUM")
quorum_value = 2

# Number of retry attempts for write consistency operations
write_retry_count = 3

# Connection pool size for each peer Redis node (for write consistency)
# This controls how many concurrent connections each peer Redis node can handle
peer_redis_pool_size = 64